name: Upgrade Spring Boot Version

on:
  schedule:
    - cron: '0 4 * * *'

permissions:
  contents: write
  pull-requests: write

jobs:
  upgrade-spring-boot:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Upgrade Spring Boot
        id: upgrade_spring_boot
        run: |
          latest_version=$(curl -s 'https://repo1.maven.org/maven2/org/springframework/boot/spring-boot/maven-metadata.xml' | grep "<latest>" | sed 's/.*<latest>\(.*\)<\/latest>.*/\1/')
          echo "Latest Spring Boot version: $latest_version"
          
          current_version=$(grep 'springBootVersion' gradle.properties | cut -d'=' -f2)
          echo "Current Spring Boot version: $current_version"
          
          echo "current_version=$current_version" >> $GITHUB_ENV
          echo "latest_version=$latest_version" >> $GITHUB_ENV
          
          if [[ "$current_version" == "$latest_version" ]]; then
            echo "Spring Boot version is up to date"
            exit 0
          fi
          
          sed -i "s/^springBootVersion=.*/springBootVersion=$latest_version/" gradle.properties
          sed -i "s/^version=.*/version=${latest_version}-SNAPSHOT/" gradle.properties 

      - name: Create Pull Request
        if: env.latest_version != env.current_version
        uses: peter-evans/create-pull-request@v7
        with:
          commit-message: "Update Spring Boot version to ${{ env.latest_version }}"
          title: "Update Spring Boot version to ${{ env.latest_version }}"
          body: ""
          branch: upgrade-spring-boot-version-${{ env.latest_version }}
