name: Build
on:
  push:
    branches:
      - main
      - 3.*
      - 2.*
  pull_request:
    branches:
      - main
      - 3.*
      - 2.*
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  lint:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: 'corretto'
      - uses: gradle/actions/setup-gradle@v4
      - name: Lint
        run: ./gradlew spotlessCheck spotbugsMain

  test:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [ lint ]
    strategy:
      matrix:
        java-version: ['17', '21', '24']
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Test
        run: ./gradlew test

  native-image-build:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Setup GraalVM
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          native-image-job-reports: 'true'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build native image
        run: |
          ./gradlew nativeRun
