apply plugin: 'java-platform'
apply plugin: 'maven-publish'
apply plugin: 'signing'

version = version as String
version = System.getenv('RELEASE') ? version.substring(0, version.lastIndexOf('-SNAPSHOT')) : version

repositories {
    mavenLocal()
    mavenCentral()
}

javaPlatform {
    allowDependencies()
}

dependencies {
    constraints {
        // autoconfigure
        api(project(":grpc-boot-autoconfigure:grpc-client-boot-autoconfigure"))
        api(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))

        // extensions
        api(project(":grpc-extensions:grpc-metrics"))
        api(project(":grpc-extensions:grpc-test"))
        api(project(":grpc-extensions:grpc-tracing"))
        api(project(":grpc-extensions:grpc-transcoding"))
        api(project(":grpc-extensions:grpc-transcoding-springdoc"))
        api(project(":grpc-extensions:grpc-validation"))

        // starters
        api(project(":grpc-starters:grpc-boot-starter"))
        api(project(":grpc-starters:grpc-client-boot-starter"))
        api(project(":grpc-starters:grpc-server-boot-starter"))
        api(project(":grpc-starters:grpc-starter-metrics"))
        api(project(":grpc-starters:grpc-starter-protovalidate"))
        api(project(":grpc-starters:grpc-starter-test"))
        api(project(":grpc-starters:grpc-starter-tracing"))
        api(project(":grpc-starters:grpc-starter-transcoding"))
        api(project(":grpc-starters:grpc-starter-transcoding-springdoc"))
        api(project(":grpc-starters:grpc-starter-validation"))

        // grpc related dependencies
        api("com.google.api:api-common:${googleApiCommonVersion}")
        api("com.google.api.grpc:proto-google-common-protos:${protoGoogleCommonProtosVersion}")
        api("javax.annotation:javax.annotation-api:${javaxValidationApiVersion}") // for using javax.annotation.Generated
    }

    api(platform("io.grpc:grpc-bom:${grpcVersion}"))
    api(platform("com.google.protobuf:protobuf-bom:${protobufVersion}"))
}

def githubUrl = 'https://github.com/DanielLiu1123/grpc-starter'

publishing {
    publications {
        maven(MavenPublication) {
            from components.javaPlatform

            pom {
                url = "${githubUrl}"
                name = project.name
                description = project.description ?: project.name
                licenses {
                    license {
                        name = 'MIT License'
                        url = 'https://opensource.org/license/mit'
                        distribution = 'repo'
                    }
                }
                developers {
                    developer {
                        id = 'Freeman'
                        name = 'Freeman Liu'
                        email = '<EMAIL>'
                    }
                }
                scm {
                    connection = "scm:git:git://${githubUrl.substring(8)}.git"
                    developerConnection = "scm:git:ssh@${githubUrl.substring(8)}.git"
                    url = "${githubUrl}"
                }
            }
        }
    }

    repositories {
        maven {
            url = "${rootDir}/build/staging-deploy"
        }
    }
}
