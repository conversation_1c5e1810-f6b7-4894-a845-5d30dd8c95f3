apply plugin: "org.rodnansol.spring-configuration-property-documenter"

// see https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc#multi-module-multiple-sub-projects
tasks.register('generateConfigurationPropertiesDocs') {
    dependsOn generateAndAggregateDocuments {
        documentName = "Configuration Properties"
        documentDescription = """
Configuration properties for the gRPC starter project.

This page was generated by [spring-configuration-property-documenter](https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc).
"""
        type = "MARKDOWN"

        metadataInputs {
            metadata {
                name = "grpc-client-boot-autoconfigure"
                input = file("grpc-boot-autoconfigure/grpc-client-boot-autoconfigure")
                excludedGroups = ["Unknown group"]
            }
            metadata {
                name = "grpc-server-boot-autoconfigure"
                input = file("grpc-boot-autoconfigure/grpc-server-boot-autoconfigure")
                excludedGroups = ["Unknown group"]
            }
            metadata {
                name = "grpc-metrics"
                input = file("grpc-extensions/grpc-metrics")
                excludedGroups = ["Unknown group"]
            }
            metadata {
                name = "grpc-test"
                input = file("grpc-extensions/grpc-test")
                excludedGroups = ["Unknown group"]
            }
            metadata {
                name = "grpc-tracing"
                input = file("grpc-extensions/grpc-tracing")
                excludedGroups = ["Unknown group"]
            }
            metadata {
                name = "grpc-transcoding"
                input = file("grpc-extensions/grpc-transcoding")
                excludedGroups = ["Unknown group"]
            }
            metadata {
                name = "grpc-validation"
                input = file("grpc-extensions/grpc-validation")
                excludedGroups = ["Unknown group"]
            }
        }

        outputFile = new File("build/configuration-properties.md")
    }
}
