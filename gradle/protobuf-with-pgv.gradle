apply plugin: 'com.google.protobuf'

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
        javapgv {
            artifact = "build.buf.protoc-gen-validate:protoc-gen-validate:${pgvVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {
                option '@generated=omit'
            }
            javapgv {
                option "lang=java"
            }
        }
    }
}
