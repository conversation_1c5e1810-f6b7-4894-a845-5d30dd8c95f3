apply plugin: 'org.jreleaser'
apply plugin: 'signing'

version = version as String
version = System.getenv('RELEASE') ? version.substring(0, version.lastIndexOf('-SNAPSHOT')) : version

// see https://jreleaser.org/guide/latest/reference/deploy/maven/maven-central.html
jreleaser {
    signing {
        active = 'RELEASE'
        armored = true
        passphrase = System.getenv('GPG_PASSPHRASE')
        publicKey  = System.getenv('GPG_PUBLIC_KEY')
        secretKey  = System.getenv('GPG_SECRET_KEY')
    }
    deploy {
        maven {
            mavenCentral {
                'release-deploy' {
                    active = 'RELEASE'
                    url = 'https://central.sonatype.com/api/v1/publisher'
                    stagingRepository("${rootDir}/build/staging-deploy")
                    username = System.getenv('MAVENCENTRAL_USERNAME')
                    password = System.getenv('MAVENCENTRAL_PASSWORD')
                }
            }
            nexus2 {
                'snapshot-deploy' {
                    active = 'SNAPSHOT'
                    url = 'https://s01.oss.sonatype.org/service/local'
                    snapshotUrl = 'https://central.sonatype.com/repository/maven-snapshots'
                    applyMavenCentralRules = true
                    snapshotSupported = true
                    closeRepository = true
                    releaseRepository = true
                    stagingRepository("${rootDir}/build/staging-deploy")
                    username = System.getenv('MAVENCENTRAL_USERNAME')
                    password = System.getenv('MAVENCENTRAL_PASSWORD')
                    sign = false
                }
            }
        }
    }
}
