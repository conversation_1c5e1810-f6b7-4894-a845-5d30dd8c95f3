pluginManagement {
    repositories {
        maven { url = 'https://repo.spring.io/milestone' }
        maven { url = 'https://repo.spring.io/snapshot' }
        gradlePluginPortal()
    }
}

rootProject.name = 'grpc-starter'

// core autoconfigure, order by name
include(":grpc-boot-autoconfigure:grpc-client-boot-autoconfigure")
include(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure")

// extensions, order by name
include(":grpc-extensions:grpc-metrics")
include(":grpc-extensions:grpc-test")
include(":grpc-extensions:grpc-tracing")
include(":grpc-extensions:grpc-transcoding")
include(":grpc-extensions:grpc-transcoding-springdoc")
include(":grpc-extensions:grpc-validation")

// bom
include(":grpc-starter-dependencies")

// starters, order by name
include(":grpc-starters:grpc-boot-starter")
include(":grpc-starters:grpc-client-boot-starter")
include(":grpc-starters:grpc-server-boot-starter")
include(":grpc-starters:grpc-starter-metrics")
include(":grpc-starters:grpc-starter-protovalidate")
include(":grpc-starters:grpc-starter-test")
include(":grpc-starters:grpc-starter-tracing")
include(":grpc-starters:grpc-starter-transcoding")
include(":grpc-starters:grpc-starter-transcoding-springdoc")
include(":grpc-starters:grpc-starter-validation")

// examples, order by name
include(":examples:metrics")
include(":examples:multi-module:api")
include(":examples:multi-module:client")
include(":examples:multi-module:server")
include(":examples:native-image")
include(":examples:protovalidate")
include(":examples:quick-start")
include(":examples:refresh")
include(":examples:tls")
include(":examples:tracing")
include(":examples:transcoding:best-practice:bp-api")
include(":examples:transcoding:best-practice:bp-server")
include(":examples:transcoding:webflux")
include(":examples:transcoding:webmvc")
include(":examples:transcoding-springdoc")
include(":examples:validation")

java.util.Optional.of(new File("${rootDir}/.git/hooks")).filter { it.exists() && it.isDirectory() }.ifPresent {
    new File("${rootDir}/.githooks").eachFile(groovy.io.FileType.FILES) {
        java.nio.file.Files.copy(it.toPath(), new File("${rootDir}/.git/hooks", it.name).toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING)
    }
}
