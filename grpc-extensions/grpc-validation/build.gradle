dependencies {
    compileOnly(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))
    compileOnly(project(":grpc-boot-autoconfigure:grpc-client-boot-autoconfigure"))

    api("com.google.protobuf:protobuf-java")
    api("io.grpc:grpc-stub")
    api("io.grpc:grpc-protobuf")

    compileOnly("build.buf.protoc-gen-validate:pgv-java-grpc:${pgvVersion}")
    compileOnly("build.buf:protovalidate:${protovalidateVersion}")
    compileOnly("org.projectnessie.cel:cel-core:0.4.4") // register reflection for ULong[]

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("build.buf.protoc-gen-validate:pgv-java-grpc:${pgvVersion}")
    testImplementation("build.buf:protovalidate:${protovalidateVersion}")
    testImplementation(project(":grpc-starters:grpc-boot-starter"))
    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation("io.github.danielliu1123:classpath-replacer:${classpathReplacerVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
apply from: "${rootDir}/gradle/protobuf-with-pgv.gradle"
