dependencies {
    api("org.springframework.boot:spring-boot-starter-test")

    compileOnly(project(":grpc-boot-autoconfigure:grpc-client-boot-autoconfigure"))
    compileOnly(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("io.grpc:grpc-testing-proto")
    testImplementation(project(":grpc-starters:grpc-boot-starter"))
}

apply from: "${rootDir}/gradle/deploy.gradle"
