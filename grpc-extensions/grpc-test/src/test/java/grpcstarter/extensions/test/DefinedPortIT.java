package grpcstarter.extensions.test;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

/**
 * Test when {@code grpc.test.server.port-type} set to {@link GrpcTestProperties.PortType#DEFINED_PORT},
 * the grpc server will start with defined port.
 *
 * <AUTHOR>
 */
@SpringBootTest(
        classes = DefinedPortIT.Cfg.class,
        properties = {"grpc.server.port=${random.int(50000,60000)}", "grpc.test.server.port-type=DEFINED_PORT"})
class DefinedPortIT {

    @LocalGrpcPort
    int port;

    @Test
    void testDefinedPort() {
        assertThat(port).isBetween(50000, 60000);
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg {}
}
