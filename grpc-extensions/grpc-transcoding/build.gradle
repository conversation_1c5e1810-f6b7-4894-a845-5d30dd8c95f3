dependencies {
    compileOnly(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))
    api("org.springframework:spring-web")
    api("com.fasterxml.jackson.core:jackson-databind")
    api("com.google.protobuf:protobuf-java-util")
    api("com.google.api:api-common:${googleApiCommonVersion}")

    compileOnly("org.springframework.boot:spring-boot-starter-web")
    compileOnly("org.springframework.boot:spring-boot-starter-webflux")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("io.grpc:grpc-testing-proto")
    testImplementation("org.springframework.boot:spring-boot-starter-web")
    testImplementation("org.springframework.boot:spring-boot-starter-webflux")
    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation(project(":grpc-starters:grpc-boot-starter"))
    testImplementation("io.github.danielliu1123:classpath-replacer:${classpathReplacerVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
apply from: "${rootDir}/gradle/protobuf.gradle"
