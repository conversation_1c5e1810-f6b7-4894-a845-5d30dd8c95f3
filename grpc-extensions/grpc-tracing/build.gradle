dependencies {
    compileOnly(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))
    compileOnly(project(":grpc-boot-autoconfigure:grpc-client-boot-autoconfigure"))

    compileOnly("org.springframework.boot:spring-boot-starter-actuator")
    compileOnly("io.micrometer:micrometer-tracing")

    compileOnly("io.micrometer:micrometer-tracing-bridge-otel")
    compileOnly("io.micrometer:micrometer-tracing-bridge-brave")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation(project(":grpc-starters:grpc-boot-starter"))
    testImplementation("org.springframework.boot:spring-boot-starter-actuator")
    testImplementation("io.micrometer:micrometer-tracing")
    testImplementation("io.github.danielliu1123:classpath-replacer:${classpathReplacerVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
