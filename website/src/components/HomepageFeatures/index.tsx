import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';

type FeatureItem = {
  title: string;
  Svg?: React.ComponentType<React.ComponentProps<'svg'>>;
  description: JSX.Element;
};

const FeatureList: FeatureItem[] = [
  {
    title: 'Easy to Use',
    description: (
      <>
          Out-of-the-box support for gRPC client/server auto-configuration.
          Make the integration of gRPC with Spring Boot feel seamless and native.
      </>
    ),
  },
  {
    title: 'Integrate gRPC Cutting-edge Ecosystem',
    description: (
      <>
          Provide missing gRPC HTTP/JSON transcoding functionality for Java ecosystem.
      </>
    ),
  },
  {
    title: 'Designed for Extension',
    description: (
      <>
          This framework is designed to be easily extended and customized.
      </>
    ),
  },
];

function Feature({title, Svg, description}: FeatureItem) {
  return (
    <div className={clsx('col col--4')}>
      <div className="text--center">
        {Svg && <Svg className={styles.featureSvg} role="img" />}
      </div>
      <div className="text--center padding-horiz--md">
        <Heading as="h3">{title}</Heading>
        <p>{description}</p>
      </div>
    </div>
  );
}

export default function HomepageFeatures(): JSX.Element {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}
