---
sidebar_position: 10
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Auto Configuration

## Dependencies

<Tabs>
    <TabItem value="gradle" label="Gradle">
        ```groovy
        implementation("io.github.danielliu1123:grpc-server-boot-starter")
        ```
    </TabItem>
    <TabItem value="maven" label="Maven">
        ```xml
        <dependency>
            <groupId>io.github.danielliu1123</groupId>
            <artifactId>grpc-server-boot-starter</artifactId>
        </dependency>
        ```
    </TabItem>
</Tabs>

## Register Services

After adding the dependencies, the application will start a gRPC server at *9090*,
and it will automatically scan and register gRPC services.
You only need to add `@Component` based annotation to your service implementations.

```java
@Component
public class SimpleServiceImpl extends SimpleServiceGrpc.SimpleServiceImplBase {
    @Override
    public void unaryRpc(SimpleRequest request, StreamObserver<SimpleResponse> responseObserver) {
        var response = SimpleResponse.newBuilder().setResponseMessage("Hello " + request.getRequestMessage()).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
```

:::info
The framework provides a `@GrpcService` annotation to better distinguish bean types.
But it is not necessary, you can use Spring's built-in `@Component`/`@Service`/`@Controller` instead.
:::

## In-process Server

gRPC server supports `in-process` transport, which is useful for testing scenarios.

```yaml
grpc:
  server:
    in-process:
      name: whatever
```

## Build-in Services

[`io.grpc:grpc-services`](https://mvnrepository.com/artifact/io.grpc/grpc-services) provides a series of built-in services.

### Health

[`Health`](https://grpc.io/docs/guides/health-checking/) service is enabled by default.

Use the following configuration to disable it:

```yaml
grpc:
  server:
    health:
      enabled: false
```

You can customize the health check logic by implementing the [`HealthChecker`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-server-boot-autoconfigure/src/main/java/grpcstarter/server/feature/health/HealthChecker.java) interface.

There are some build-in `HealthChecker` implementations:

- [`DataSourceHealthChecker`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-server-boot-autoconfigure/src/main/java/grpcstarter/server/feature/health/datasource/DataSourceHealthChecker.java) is enabled by default when the `spring-boot-starter-jdbc` is on the classpath.
- [`RedisHealthChecker`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-server-boot-autoconfigure/src/main/java/grpcstarter/server/feature/health/redis/RedisHealthChecker.java) is enabled by default when the `spring-boot-starter-data-redis` is on the classpath.

Use the following configuration to disable them:

```yaml
grpc:
  server:
    health:
      datasource:
        enabled: false
      redis:
        enabled: false
```

Starting from version 3.4.0, to better support [Kubernetes Probes](https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/),
three special services were introduced: `startup`, `readiness`, and `liveness`.

For `startup` and `readiness`, the system will return the `SERVING` status only when all `HealthChecker`s pass their checks (return `true`), as they rely on the state of external services.

For `liveness`, it always returns the `SERVING` status, as it does not rely on the state of external services.

This is suitable for most applications.
If you need more customization, you can implement the [`health`](https://github.com/grpc/grpc/blob/master/src/proto/grpc/health/v1/health.proto) service yourself:

```java
@Component
public class HealthService extends HealthGrpc.HealthImplBase {
    @Override
    public void check(HealthCheckRequest request, StreamObserver<HealthCheckResponse> responseObserver) {
        // Your custom health check logic
        var result = HealthCheckResponse.ServingStatus.SERVING;
        responseObserver.onNext(
                HealthCheckResponse.newBuilder().setStatus(result).build());
        responseObserver.onCompleted();
    }
}
```

### Reflection

[`Reflection`](https://grpc.io/docs/guides/reflection/) is disabled by default, it's often used in development and debugging scenarios.

Use the following configuration to enable it:

```yaml
grpc:
  server:
    reflection:
      enabled: true
```

### Channelz

[`Channelz`](https://grpc.io/blog/a-short-introduction-to-channelz/) is disabled by default.

Use the following configuration to enable it:

```yaml
grpc:
  server:
    channelz:
      enabled: true
```

## Virtual Threads Support

Starting from version [3.5.3.2](https://github.com/DanielLiu1123/grpc-starter/releases/tag/v3.5.3.2), gRPC server supports virtual threads when using Java 21+.

When `spring.threads.virtual.enabled` is set to `true`, the gRPC server will automatically use virtual threads to handle requests, providing better performance for I/O-bound operations.

```yaml
spring:
  threads:
    virtual:
      enabled: true
```

:::info
Virtual threads are only available in Java 21+. This feature will be ignored on older Java versions.
:::

## Configurations

Full configuration is available in the [`GrpcServerProperties`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-server-boot-autoconfigure/src/main/java/grpcstarter/server/GrpcServerProperties.java).
