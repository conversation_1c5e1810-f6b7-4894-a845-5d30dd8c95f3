---
sidebar_position: 30
---

# Extension Points

## ServerInterceptor

[`io.grpc.ServerInterceptor`](https://grpc.github.io/grpc-java/javadoc/io/grpc/ServerInterceptor.html) is an interceptor to intercept the lifecycle of a gRPC call on the server side.

Register a `ServerInterceptor` for logging all incoming messages:

```java
@Bean
class LoggingServerInterceptor implements ServerInterceptor {
    private static final Logger log = LoggerFactory.getLogger(LoggingServerInterceptor.class);

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        return new ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT>(next.startCall(call, headers)) {
            @Override
            public void onMessage(ReqT message) {
                log.info("Received message: {}", message);
                super.onMessage(message);
            }
        };
    }
}
```

Multiple `ServerInterceptor` will be called in the order defined by the Spring Bean's order, from smallest to largest.

## GrpcServerCustomizer

`GrpcServerCustomizer` customize the gRPC `Server` before it is created.

Register a `GrpcServerCustomizer` to set the maximum inbound message size for the gRPC server:

```java
@Bean
class MaxInboundMessageSizeGrpcServerCustomizer implements GrpcServerCustomizer {
    @Override
    public void customize(ServerBuilder<?> serverBuilder) {
        serverBuilder.maxInboundMessageSize(16 * 1024 * 1024);
    }
}
```