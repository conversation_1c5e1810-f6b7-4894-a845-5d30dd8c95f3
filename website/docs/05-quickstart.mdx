---
sidebar_position: 5
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Quick Start

## Managing Dependencies

To simplify dependency management, this project provides a BOM (Bill of Materials). 
The BOM ensures that all dependencies are compatible and their versions are managed centrally.

:::info
When using `grpc-starter-dependencies`, you do not need to specify versions for the dependencies listed in the BOM. The BOM will handle the versioning for you.
:::

<Tabs>
    <TabItem value="gradle" label="Gradle">
        ```groovy
        implementation(platform("io.github.danielliu1123:grpc-starter-dependencies:<latest>"))
        implementation("io.github.danielliu1123:grpc-boot-starter")
        implementation("io.grpc:grpc-testing-proto")
        ```
    </TabItem>
    <TabItem value="maven" label="Maven">
        ```xml
        <dependencies>
            <dependency>
                <groupId>io.github.danielliu1123</groupId>
                <artifactId>grpc-boot-starter</artifactId>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-testing-proto</artifactId>
            </dependency>
        </dependencies>
        <dependencyManagement>
            <dependencies>
                <dependency>
                    <groupId>io.github.danielliu1123</groupId>
                    <artifactId>grpc-starter-dependencies</artifactId>
                    <version>latest</version>
                    <type>pom</type>
                    <scope>import</scope>
                </dependency>
            </dependencies>
        </dependencyManagement>
        ```
    </TabItem>
</Tabs>

## Example

```java
@SpringBootApplication
public class QuickStartApp extends SimpleServiceGrpc.SimpleServiceImplBase {

    public static void main(String[] args) {
        new SpringApplicationBuilder(QuickStartApp.class)
                .properties("grpc.client.base-packages=io.grpc") // scan packages for gRPC clients
                .properties("grpc.client.authority=127.0.0.1:9090") // default authority for gRPC clients
                .run(args);
    }

    @Override
    public void unaryRpc(SimpleRequest request, StreamObserver<SimpleResponse> r) {
        var response = SimpleResponse.newBuilder()
                .setResponseMessage("Hello " + request.getRequestMessage())
                .build();
        r.onNext(response);
        r.onCompleted();
    }

    @Bean
    ApplicationRunner runner(SimpleServiceGrpc.SimpleServiceBlockingStub stub) { // inject gRPC client
        return args -> {
            var response = stub.unaryRpc(SimpleRequest.newBuilder().setRequestMessage("World!").build());
            System.out.println(response.getResponseMessage());
        };
    }
}
```

No additional annotations or classes are required! Simply run the application and you will see the `Hello World!` output.

You can find the source code at [quick-start](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/quick-start).

:::tip
Setting up the Protobuf plugin: <br/>
For Gradle: Refer to the [multi-module-example](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/multi-module). <br/>
For Maven: Refer to the [grpc-starter-maven-demo](https://github.com/DanielLiu1123/grpc-starter-maven-demo).
:::
