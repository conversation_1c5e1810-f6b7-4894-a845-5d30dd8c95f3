---
sidebar_position: 10
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Auto Configuration

## Dependencies

<Tabs>
    <TabItem value="gradle" label="Gradle">
        ```groovy
        implementation("io.github.danielliu1123:grpc-client-boot-starter")
        ```
    </TabItem>
    <TabItem value="maven" label="Maven">
        ```xml
        <dependency>
            <groupId>io.github.danielliu1123</groupId>
            <artifactId>grpc-client-boot-starter</artifactId>
        </dependency>
        ```
    </TabItem>
</Tabs>

## Usage

Steps to autoconfigure gRPC clients (stubs):
1. Specify the clients to be added as Spring Beans.
2. Configure request addresses for clients.

Then you can inject the clients using whatever method you prefer.

### Specify Stubs

You can configure client classes in two ways.

#### @EnableGrpcClients

`@EnableGrpcClients` can configure packages to scan or directly specify client classes.

Configure packages to scan with `basePackages`:

```java
@EnableGrpcClients(basePackages = "io.grpc")
```

Specify client classes directly with `clients`:

```java
@EnableGrpcClients(clients = { SimpleServiceGrpc.SimpleServiceBlockingStub.class })
```

You can also combine both ways:

```
@EnableGrpcClients(basePackages = "io.grpc", clients = { SimpleServiceGrpc.SimpleServiceBlockingStub.class })
```

:::tip
It's recommended to use `clients` to specify client classes directly to avoid adding unnecessary beans into the container,
which can improve startup speed.
:::

#### Configuration

Similar to `@EnableGrpcClients`, you can also specify packages to scan or client classes via configuration files.

```yaml
grpc:
  client:
    base-packages: [ io.grpc ]
    stubs: [ io.grpc.testing.protobuf.SimpleServiceGrpc.SimpleServiceBlockingStub ]
```

### Configure Request Addresses

You can configure different request addresses (authority) for different client classes,
clients configured with the same channel will reuse that channel.

```yaml
grpc:
  client:
    channels:
      - authority: localhost:9090
        stubs: [ io.grpc.*BlockingStub ]
      - authority: localhost:9091
        stubs: [ io.grpc.*FutureStub ]
```

There are three ways to configure the clients for a channel:

- Specify the fully qualified class name of the client using `classes`.

  ```yaml
  grpc:
    client:
      channels:
        - authority: localhost:9090
          classes: [ io.grpc.testing.protobuf.SimpleServiceGrpc.SimpleServiceBlockingStub ]
    ```

- Use `stubs` to specify client names with Ant-style patterns.

  ```yaml
  grpc:
    client:
      channels:
        - authority: localhost:9090
          stubs: [ io.grpc.*BlockingStub ]
    ```

- Specify the service name using `services`, e.g. `io.grpc.health.v1.HealthGrpc#SERVICE_NAME`.

  ```yaml
  grpc:
    client:
      channels:
        - authority: localhost:9090
          services: [ grpc.testing.SimpleService ]
    ```

:::info
The priority of the configuration methods is `classes` > `stubs` > `services`。
:::

### Inject Client

Inject the client using whatever method you prefer, constructor, `@Autowired`, setter...

It's just like injecting any other Spring Bean, and respects the Spring Bean lifecycle.

```java
@Component
class MyComponent {

    @Autowired
    private SimpleServiceGrpc.SimpleServiceBlockingStub stub;

}
```

## Virtual Threads Support

Starting from version [*******](https://github.com/DanielLiu1123/grpc-starter/releases/tag/v*******), gRPC client supports virtual threads when using Java 21+.

When `spring.threads.virtual.enabled` is set to `true`, the gRPC client channels will automatically use virtual threads for handling requests.

```yaml
spring:
  threads:
    virtual:
      enabled: true
```

:::info
Virtual threads are only available in Java 21+. This feature will be ignored on older Java versions.
:::

## Configurations

Configure `deadline` for the channel.

```yaml
grpc:
  client:
    channels:
      - authority: localhost:9090
        stubs: [ io.grpc.*BlockingStub ]
        deadline: 5000
```

Full configuration is available in the [`GrpcClientProperties`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-client-boot-autoconfigure/src/main/java/grpcstarter/client/GrpcClientProperties.java).
