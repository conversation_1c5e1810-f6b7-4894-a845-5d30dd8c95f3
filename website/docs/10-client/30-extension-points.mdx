---
sidebar_position: 30
---

# Extension Points

## ClientInterceptor

[`io.grpc.ClientInterceptor`](https://grpc.github.io/grpc-java/javadoc/io/grpc/ClientInterceptor.html) is an interceptor to intercept the lifecycle of a gRPC call on the client side.

Register a `ClientInterceptor` for all gRPC stubs:

```java
@Component
public class LoggingClientInterceptor implements ClientInterceptor {
    private static final Logger log = LoggerFactory.getLogger(LoggingClientInterceptor.class);

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(MethodDescriptor<ReqT, RespT> method, CallOptions callOptions, Channel next) {
        return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(next.newCall(method, callOptions)) {
            @Override
            public void sendMessage(ReqT message) {
                log.info("Sending message: {}", message);
                super.sendMessage(message);
            }
        };
    }
}
```

Multiple `ClientInterceptor` will be called in the order defined by the Spring Bean's order, from smallest to largest.

## GrpcChannelCustomizer

[`GrpcChannelCustomizer`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-client-boot-autoconfigure/src/main/java/grpcstarter/client/GrpcChannelCustomizer.java) customize the `Channel` before it is created.

Register a `GrpcChannelCustomizer` to set the retry attempts for the gRPC channels:

```java
@Component
public class RetryGrpcChannelCustomizer implements GrpcChannelCustomizer {
    @Override
    public void customize(GrpcClientProperties.Channel channelConfig, ManagedChannelBuilder<?> channelBuilder) {
        channelBuilder.enableRetry().maxRetryAttempts(3);
    }
}
```

Multiple `GrpcChannelCustomizer` will be called in the order defined by the Spring Bean's order, from smallest to largest.

## GrpcClientBeanDefinitionHandler

Starting from [`*******`](https://github.com/DanielLiu1123/grpc-starter/releases/tag/v*******),
[`GrpcClientBeanDefinitionHandler`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-client-boot-autoconfigure/src/main/java/grpcstarter/client/GrpcClientBeanDefinitionHandler.java)
is used to post-process `BeanDefinition` before the gRPC client bean is registered.

You can set `beanDefinitionHandler` by `@EnableGrpcClient` or configuring `grpc.client.bean-definition-handler`.
If both are set, the one set by `@EnableGrpcClient` will be used.

If you want only blocking stub to be registered:

```java
@EnableGrpcClients(beanDefinitionHandler = GrpcClientBeanDefinitionHandler.Blocking.class)
```

The framework provides some built-in `GrpcClientBeanDefinitionHandler` implementations:

- `GrpcClientBeanDefinitionHandler.Default`: Register all stubs.
- `GrpcClientBeanDefinitionHandler.Blocking`: Only register blocking stub.
- `GrpcClientBeanDefinitionHandler.Future`: Only register future stub.
- `GrpcClientBeanDefinitionHandler.Async`: Only register async stub.

## Deep Customization

### Manual Bean Creation

If you're not happy with the autoconfigured gRPC client bean,
you can configure it using the Spring original approach (`@Bean`).

If you manually create the gRPC client bean, the autoconfigured gRPC client bean will not be created.

```java
@Configuration
class GrpcClientConfiguration {
    @Bean
    public SimpleServiceGrpc.SimpleServiceBlockingStub simpleServiceBlockingStub() {
        var channel = ManagedChannelBuilder.forAddress("localhost", 9090)
                .usePlaintext()
                .build();
        return SimpleServiceGrpc.newBlockingStub(channel);
    }
}
```

### ManagedChannels API

Starting from [`3.5.3`](https://github.com/DanielLiu1123/grpc-starter/releases/tag/v3.5.3),
you can use [`ManagedChannels`](https://github.com/DanielLiu1123/grpc-starter/blob/main/grpc-boot-autoconfigure/grpc-client-boot-autoconfigure/src/main/java/grpcstarter/client/ManagedChannels.java)
to access configured gRPC channels by name.

Configure multiple named channels:

```yaml
grpc:
  client:
    channels:
      - name: "service-a"
        authority: "localhost:9090"
      - name: "service-b"
        authority: "localhost:9091"
```

Use `ManagedChannels` to create custom stub beans:

```java
@Configuration
class CustomStubConfiguration {

    @Bean
    public HealthGrpc.HealthBlockingStub customHealthStub(ManagedChannels channels) {
        ManagedChannel channel = channels.getChannel("service-a");
        return HealthGrpc.newBlockingStub(channel);
    }
}
```
