---
sidebar_position: 20
---

# Dynamic Refresh

The dynamic refresh feature enables the automatic updating of gRPC stubs without restarting your application. 
Configuration settings prefixed with `grpc.client` can be placed in any configuration center, 
such as [<PERSON>](https://github.com/hashicorp/consul), [<PERSON>](https://github.com/apolloconfig/apollo), or [Nacos](https://github.com/alibaba/nacos). 
When these configurations (e.g., `authority`, `max-inbound-message-size`) are modified, the gRPC stubs will refresh automatically.

This feature is disabled by default, you can enable it by using the following configuration:

```yaml
grpc:
  client:
    refresh:
      enabled: true
```

:::info
This feature requires `spring-cloud-context` in the classpath and a `RefreshEvent` to be published.
:::
