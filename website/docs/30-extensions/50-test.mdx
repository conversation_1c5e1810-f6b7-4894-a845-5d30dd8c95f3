---
sidebar_position: 50
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Test

This extension integrates `@SpringBootTest` for better testing experience.

## Dependencies

<Tabs>
    <TabItem value="gradle" label="Gradle">
        ```groovy
        testImplementation("io.github.danielliu1123:grpc-starter-test")
        ```
    </TabItem>
    <TabItem value="maven" label="Maven">
        ```xml
        <dependency>
            <groupId>io.github.danielliu1123</groupId>
            <artifactId>grpc-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        ```
    </TabItem>
</Tabs>

## Usage

### @InProcessName

After adding the dependency, the gRPC server will **use in-process by default**.

Get the in-process name using the `@InProcessName` annotation:

```java
@SpringBootTest
class FooTest {

    @InProcessName
    String inProcessName;
}
```

### @LocalGrpcPort

Specify the server port type using the `grpc.test.server.port-type` configuration, the value can be:

- `IN_PROCESS`: using in-process. The in-process name can be obtained using the `@InProcessName` annotation.
This is the default value.
- `RANDOM_PORT`: Use a random port. The port number can be obtained using the `@LocalGrpcPort` annotation.
- `DEFINED_PORT`: Use the defined port, which is the value of `grpc.server.port`.
- `NONE`: Do not start the server.

Get random port using the `@LocalGrpcPort` annotation:

```java
@SpringBootTest(properties = "grpc.test.server.port-type=RANDOM_PORT")
class FooTest {

    @LocalGrpcPort
    int port;
}
```

## Example

```java
@SpringBootTest(classes = InProcessNameIT.Cfg.class)
class InProcessNameIT {

    @InProcessName
    String name;

    @Test
    void testInProcessName() {
        var stub = StubUtil.createStub(name, SimpleServiceGrpc.SimpleServiceBlockingStub.class);
        var responseMessage = stub.unaryRpc(SimpleRequest.newBuilder().setRequestMessage("World!").build())
                .getResponseMessage();

        assertThat(responseMessage).isEqualTo("Hello World!");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg extends SimpleServiceGrpc.SimpleServiceImplBase {
        @Override
        public void unaryRpc(SimpleRequest request, StreamObserver<SimpleResponse> responseObserver) {
            responseObserver.onNext(SimpleResponse.newBuilder()
                    .setResponseMessage("Hello " + request.getRequestMessage())
                    .build());
            responseObserver.onCompleted();
        }
    }
}
```

## Configurations

If you want to disable the test extension, use the following configuration:

```yaml
grpc:
  test:
    enabled: false
```
