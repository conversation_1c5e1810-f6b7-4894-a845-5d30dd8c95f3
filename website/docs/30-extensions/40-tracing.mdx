---
sidebar_position: 40
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Tracing

This extension integrates `spring-boot-starter-actuator` to support distributed tracing for gRPC client and server.

## Dependencies

<Tabs>
    <TabItem value="gradle" label="Gradle">
        ```groovy
        implementation("io.github.danielliu1123:grpc-starter-tracing")
        ```
    </TabItem>
    <TabItem value="maven" label="Maven">
        ```xml
        <dependency>
            <groupId>io.github.danielliu1123</groupId>
            <artifactId>grpc-starter-tracing</artifactId>
        </dependency>
        ```
    </TabItem>
</Tabs>

## Example

Refer to [tracing example](https://github.com/DanielLiu1123/grpc-starter/tree/main/examples/tracing).

## Configurations

If you want to disable the tracing, using the following configuration:

```yaml
grpc:
  tracing:
    enabled: false
```

Only disable the tracing for the client:

```yaml
grpc:
  tracing:
    client:
      enabled: false
```

Only disable the tracing for the server:

```yaml
grpc:
  tracing:
    server:
      enabled: false
```

The tracing processing is implemented through `ClientInterceptor` and `ServerInterceptor`.
The order of interceptors can be customized.
The default order is 0.

```yaml
grpc:
  tracing:
    client:
      order: 0
    server:
      order: 0
```
