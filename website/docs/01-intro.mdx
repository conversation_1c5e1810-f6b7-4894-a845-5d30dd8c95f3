---
sidebar_position: 1
---

# Introduction

`grpc-starter` is a comprehensive suite of Spring Boot starters designed for the gRPC ecosystem. It offers seamless autoconfiguration and highly extensible capabilities to enhance the integration between Spring Boot and gRPC.

## Why Choose grpc-starter?

While there are existing gRPC Spring Boot starters such as:

- [grpc-spring](https://github.com/grpc-ecosystem/grpc-spring)
- [grpc-spring-boot-starter](https://github.com/LogNet/grpc-spring-boot-starter)

`grpc-starter` was created to address several limitations and provide additional features:

1. **Elegant Client Autoconfiguration**: Unlike other starters that require additional annotations like `@GrpcClient`, `grpc-starter` allows direct injection of gRPC clients using `@Autowired`, fully adhering to the Spring bean lifecycle.
2. **Comprehensive Ecosystem Integration**: It includes features like gRPC HTTP transcoding, Protobuf validation, and test support, which are often missing in other starters.
3. **Ease of Extension**: It provides suitable extension points, making it easier to write and integrate custom extensions.

## Core Features

### Client
- **Autoconfiguration**: Automatically configure gRPC clients, allowing direct injection with `@Autowired` while fully respecting the Spring Bean lifecycle.
- **Dynamic Refreshing**: Modify gRPC client configurations at runtime without restarting the application.

### Server
- **Autoconfiguration**: Write your gRPC service implementation and expose it as a Spring bean. The framework handles the rest.
- **Exception Handling**: Provides `@ExceptionHandler`-like exception handling for gRPC servers through `@GrpcAdvice` and `@GrpcExceptionHandler`.
- **Health Check**: Built-in health check support for gRPC servers.

### Extensions
- **gRPC HTTP Transcoding**: Enable a single codebase to support both gRPC and HTTP/JSON, similar to [grpc-gateway](https://github.com/grpc-ecosystem/grpc-gateway) but for Java.
- **Protobuf Validation**: Integrate with [protovalidate](https://github.com/bufbuild/protovalidate) and [protoc-gen-validate](https://github.com/bufbuild/protoc-gen-validate) for Protobuf message validation.
- **Metrics**: Support for metrics through Spring Boot Actuator.
- **Tracing**: Support for tracing through Spring Boot Actuator.
- **Testing**: Integration with `SpringBootTest` for comprehensive testing support.

This framework is designed with extensibility in mind, allowing you to easily extend it to meet your specific needs.

## Project Structure

```text
├── examples                                # Examples
├── grpc-boot-autoconfigure
│   ├── grpc-client-boot-autoconfigure      # gRPC client autoconfiguration
│   └── grpc-server-boot-autoconfigure      # gRPC server autoconfiguration
├── grpc-extensions
│   ├── grpc-metrics                        # Metrics extension
│   ├── grpc-test                           # Test extension
│   ├── grpc-tracing                        # Tracing extension
│   ├── grpc-transcoding                    # HTTP transcoding extension
│   ├── grpc-transcoding-springdoc          # HTTP transcoding with OpenAPI integration
│   └── grpc-validation                     # Protobuf validation extension
├── grpc-starter-dependencies               # BOM
├── grpc-starters
│   ├── grpc-boot-starter                   # Core starter, including grpc-client-boot-starter and grpc-server-boot-starter
│   ├── grpc-client-boot-starter            # gRPC client starter
│   ├── grpc-server-boot-starter            # gRPC server starter
│   ├── grpc-starter-metrics                # Metrics starter
│   ├── grpc-starter-protovalidate          # Protobuf validation starter, implemented by protovalidate
│   ├── grpc-starter-test                   # Test starter
│   ├── grpc-starter-tracing                # Tracing starter
│   ├── grpc-starter-transcoding            # HTTP transcoding starter for WebMVC/WebFlux
│   ├── grpc-starter-transcoding-springdoc  # HTTP transcoding starter with OpenAPI integration
│   └── grpc-starter-validation             # Protobuf validation starter, implemented by protoc-gen-validate
└── website                                 # Documentation, powered by Docusaurus
```

:::tip
Always uses the *starters* instead of the autoconfigure modules directly.
:::
