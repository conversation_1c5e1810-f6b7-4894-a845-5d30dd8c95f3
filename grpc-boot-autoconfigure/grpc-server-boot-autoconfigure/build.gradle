dependencies {
    api("org.springframework.boot:spring-boot-starter")
    api("io.grpc:grpc-core")
    api("io.grpc:grpc-inprocess") // from 1.58.0, InProcessServerBuilder moved to grpc-inprocess
    api("io.grpc:grpc-protobuf")
    api("io.grpc:grpc-services")

    // health check support
    compileOnly("org.springframework.boot:spring-boot-starter-jdbc")
    compileOnly("org.springframework.boot:spring-boot-starter-data-redis")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testRuntimeOnly("io.grpc:grpc-netty-shaded")
    testImplementation("io.grpc:grpc-testing-proto")
    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation(project(":grpc-starters:grpc-client-boot-starter"))
}

apply from: "${rootDir}/gradle/deploy.gradle"
