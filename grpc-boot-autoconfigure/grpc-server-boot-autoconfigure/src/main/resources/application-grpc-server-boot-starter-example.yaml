spring:
  threads:
    virtual:
      enabled: true                             # enable virtual threads (Java 21+)

grpc:
  server:
    enabled: true                               # whether to enable grpc server
    port: 9090                                  # grpc server port
    max-inbound-message-size: 16MB              # max message size
    max-inbound-metadata-size: 16KB             # max metadata size
    shutdown-timeout: 5000                      # graceful shutdown timeout, 0 means no timeout
    reflection:
      enabled: true                             # whether to register reflection service
    health:
      enabled: true                             # whether to enable health check
      datasource:
        enabled: true                           # whether to enable datasource health check
        service: ds                             # datasource service name
        timeout: 2                              # datasource health check timeout, unit: second
      redis:
        service: redis                          # redis service name
        enabled: true                           # whether to enable redis health check
    exception-handling:
      enabled: true                             # whether to enable exception handling
      default-exception-advice-enabled: true    # whether to enable default exception advice
    in-process:
      name: test                                # in-process server name
    channelz:
      enabled: true                             # whether to enable channelz
      max-page-size: 100                        # max page size
    tls:
      key-manager:
        cert-chain: classpath:server.crt
        private-key: classpath:server.key
    response:
      max-description-length: 2048              # max description length