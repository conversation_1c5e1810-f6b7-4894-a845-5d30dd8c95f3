package grpcstarter.server;

import io.grpc.Server;
import org.springframework.context.ApplicationEvent;

/**
 * Grpc server terminated event, triggered when the gRPC server is terminated.
 *
 * <AUTHOR>
 */
public class GrpcServerTerminatedEvent extends ApplicationEvent {

    public GrpcServerTerminatedEvent(Server source) {
        super(source);
    }

    @Override
    public Server getSource() {
        return ((Server) super.getSource());
    }
}
