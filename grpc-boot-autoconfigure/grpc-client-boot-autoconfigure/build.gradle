dependencies {
    api("org.springframework.boot:spring-boot-starter")
    api("io.grpc:grpc-core")
    api("io.grpc:grpc-inprocess") // from 1.58.0, InProcessChannelBuilder moved to grpc-inprocess
    api("io.grpc:grpc-protobuf")
    api("io.grpc:grpc-stub")

    compileOnly(project(":grpc-boot-autoconfigure:grpc-server-boot-autoconfigure"))
    // dynamic refresh configuration for grpc clients
    compileOnly("org.springframework.cloud:spring-cloud-context:${springCloudCommonsVersion}")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation("io.grpc:grpc-testing-proto")
    testImplementation(project(":grpc-starters:grpc-server-boot-starter"))
    testImplementation(project(":grpc-starters:grpc-starter-test"))
    testImplementation("org.springframework.cloud:spring-cloud-context:${springCloudCommonsVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
