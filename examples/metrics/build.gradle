plugins {
    id 'org.springframework.boot'
}

dependencies {
    implementation("io.grpc:grpc-testing-proto")
    implementation(project(":grpc-starters:grpc-boot-starter"))
    implementation(project(":grpc-starters:grpc-starter-metrics"))

    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-aop")
    implementation("io.micrometer:micrometer-registry-prometheus")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}
