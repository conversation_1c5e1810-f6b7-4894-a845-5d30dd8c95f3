syntax = "proto3";

package foo;

import "google/protobuf/empty.proto";
import "buf/validate/validate.proto";
import "google/api/annotations.proto";

message Foo {
  string id = 1 [(buf.validate.field).cel = {
    id: "id",
    message: "at least 5 characters",
    expression: "this.size() >= 5",
  }];
  string name = 2 [(buf.validate.field).string = {min_len: 5}];
  repeated string hobbies = 3 [(buf.validate.field).repeated = {min_items: 1}];

  option (buf.validate.message).cel = {
    id: "foo",
    message: "not a valid Foo",
    expression: "this.name != 'aaaaa' && !('coding' in this.hobbies)",
  };
}

service FooService {
  rpc InsertFoo (Foo) returns (Foo) {}
}
