plugins {
    id "org.springframework.boot"
}

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:${springdocVersion}")
    implementation("io.grpc:grpc-testing-proto")
    implementation(project(":grpc-starters:grpc-server-boot-starter"))
    implementation(project(":grpc-starters:grpc-starter-transcoding-springdoc"))

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}

apply from: "${rootDir}/gradle/protobuf.gradle"
