spring:
  ssl:
    bundle:
      pem:
        server-bundle:
          keystore:
            certificate: "classpath:server.crt"
            private-key: "classpath:server.key"
        client-bundle:
          truststore:
            certificate: "classpath:ca.crt"

grpc:
  server:
    reflection:
      enabled: true
    ssl-bundle: server-bundle
  client:
    authority: localhost:${grpc.server.port:9090}
    ssl-bundle: client-bundle
