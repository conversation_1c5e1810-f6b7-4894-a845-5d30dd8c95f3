// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user/v1/user.proto

// Protobuf Java Version: 3.25.1
package user.v1;

public interface GetUserResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:user.v1.GetUserResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.user.v1.User user = 1 [json_name = "user"];</code>
   * @return Whether the user field is set.
   */
  boolean hasUser();
  /**
   * <code>.user.v1.User user = 1 [json_name = "user"];</code>
   * @return The user.
   */
  user.v1.User getUser();
  /**
   * <code>.user.v1.User user = 1 [json_name = "user"];</code>
   */
  user.v1.UserOrBuilder getUserOrBuilder();
}
