// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user/v1/user.proto

// Protobuf Java Version: 3.25.1
package user.v1;

public interface DeleteUserRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:user.v1.DeleteUserRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 id = 1 [json_name = "id", (.buf.validate.field) = { ... }</code>
   * @return The id.
   */
  long getId();
}
