syntax = "proto3";

import "google/api/annotations.proto";

package transcoding.mvc;

message SimpleRequest {
  string requestMessage = 1;
}

message SimpleResponse {
  string responseMessage = 1;
}

service SimpleService {
  rpc UnaryRpc (SimpleRequest) returns (SimpleResponse) {
    option (google.api.http) = {
      post: "/unary",
      body: "*"
    };
  }

  rpc ServerStreamingRpc (SimpleRequest) returns (stream SimpleResponse) {
    option (google.api.http) = {
      get: "/serverstreaming"
    };
  }
}
