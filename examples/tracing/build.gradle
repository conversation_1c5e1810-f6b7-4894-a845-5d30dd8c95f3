plugins {
    id 'org.springframework.boot'
}

dependencies {
    implementation("io.grpc:grpc-testing-proto")
    implementation(project(":grpc-starters:grpc-boot-starter"))
    implementation(project(":grpc-starters:grpc-starter-tracing"))

    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("io.micrometer:micrometer-tracing-bridge-otel")
    implementation("io.opentelemetry:opentelemetry-exporter-otlp")

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}
